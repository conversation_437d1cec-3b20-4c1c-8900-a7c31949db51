﻿using Apify.SDK.Actors.WebsiteContentCrawler;
using Apify.SDK.Api;
using Apify.SDK.Client;
using Apify.SDK.Model;
using CommunityToolkit.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Phlex.Core.Apify.Extensions;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;
using Polly;
using System.Globalization;
using Task = System.Threading.Tasks.Task;

namespace Phlex.Core.Apify;

public class ApifyClient : IApifyClient
{
    private readonly ILogger<ApifyClient> logger;
    private readonly HttpClient httpClient;
    private readonly IDownloadService downloadService;
    private readonly IDatasetService datasetService;
    private readonly ICrawlerConfigurationService crawlerConfigurationService;
    private readonly ActorRunsApi runsApi;
    private readonly ActorTasksApi tasksApi;
    private readonly SchedulesApi schedulesApi;
    private readonly StorageDatasetsApi datasetsApi;
    private readonly StorageKeyValueStoresApi keyValueStoresApi;
    private readonly WebhooksWebhooksApi webhooksApi;
    private readonly LogsApi logsApi;

    private readonly ApifyConfiguration apifyConfiguration;

    // If NULL the default timeout is 360.000 seconds (100h)
    private readonly decimal? defaultTimeoutSeconds;
    // If null the default memory is 8192 MB. Running tasks with 2 CPU cores
    private readonly decimal? defaultMemoryBytes;

    private const int MaxBatchSize = 50;

    public ApifyClient(
        IConfiguration configuration,
        HttpClient httpClient,
        IDownloadService downloadService,
        IDatasetService datasetService,
        ICrawlerConfigurationService crawlerConfigurationService,
        ILogger<ApifyClient> logger)
    {
        this.logger = logger;

        var retryPolicy = Policy<HttpResponseMessage>
          .Handle<Exception>()
          .WaitAndRetryAsync(2, retryAttempt =>
          TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));

        RetryConfiguration.AsyncRetryPolicy = retryPolicy;

        apifyConfiguration = ApifyConfiguration.Get(configuration);
        datasetsApi = new StorageDatasetsApi(httpClient, apifyConfiguration.BaseUri);
        keyValueStoresApi = new StorageKeyValueStoresApi(httpClient, apifyConfiguration.BaseUri);

        runsApi = new ActorRunsApi(httpClient, apifyConfiguration.BaseUri);
        tasksApi = new ActorTasksApi(httpClient, apifyConfiguration.BaseUri);
        schedulesApi = new SchedulesApi(httpClient, apifyConfiguration.BaseUri);
        webhooksApi = new WebhooksWebhooksApi(httpClient, apifyConfiguration.BaseUri);
        logsApi = new LogsApi(httpClient, apifyConfiguration.BaseUri);

        this.downloadService = downloadService;
        this.datasetService = datasetService;
        this.crawlerConfigurationService = crawlerConfigurationService;
        this.httpClient = httpClient;
        
        defaultMemoryBytes = (decimal)SupportedMemoryMBytes.oneCpuCore;
        defaultTimeoutSeconds = 3600;
    }

    public async Task<ActorTasksGet200Response> GetTasksAsync(CancellationToken cancellationToken = default)
    {
        double? offset = null;
        double? limit = null;
        bool? desc = null;

        try
        {
            var response = await tasksApi.ActorTasksGetAsync(offset, limit, desc, cancellationToken);
            return response;
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task<string> CreateTaskAsync(string taskName, List<string> urls, int crawlDepth, CancellationToken cancellationToken = default)
    {
        WebScraperInput input = new WebScraperInput();
        if (urls.Count > 0)
        {
            List<StartUrl> startUrls = new List<StartUrl>();
            foreach (string url in urls)
            {
                startUrls.Add(new StartUrl(url, "GET"));
            }
            input.startUrls = startUrls;
        }

        input.maxCrawlDepth = crawlDepth;

        return await CreateTaskAsync(taskName, input, cancellationToken);
    }

    public async Task<string> CreateTaskAsync(string taskName, WebScraperInput input, CancellationToken cancellationToken = default)
    {
        var tasksOptions = new CreateTaskRequestOptions
        {
            Build = null!,
            TimeoutSecs = defaultTimeoutSeconds,
            MemoryMbytes = defaultMemoryBytes
        };

        try
        {
            var taskId = string.Empty;
            var actorTasksPostRequest = new ActorTasksPostRequest(Actors.WebsiteContentCrawler, taskName, tasksOptions);
            var response = await tasksApi.ActorTasksPostAsync(actorTasksPostRequest, cancellationToken);
            if (response != null)
            {
                await tasksApi.ActorTaskInputPutAsync(response.Data.Id, input, cancellationToken);
                taskId = response.Data.Id;
                logger.LogInformation("Apify: Task {TaskId} has been created.", taskId);
            }

            return taskId;
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task<string> RunTaskAsync(string taskId, CancellationToken cancellationToken = default)
    {
        object? body = null;
        double? timeout = null;
        double? memory = null;
        double? maxItems = null;
        double? maxTotalChargeUsd = null;
        string? build = null;
        double? waitForFinish = null;
        string? webhooks = null;

        string? runId = null!;
        try
        {
            var response = await tasksApi.ActorTaskRunsPostAsync(taskId, body, timeout, memory, maxItems, maxTotalChargeUsd, build, waitForFinish, webhooks, cancellationToken);
            if (response != null)
            {
                runId = response.Data.Id;
                logger.LogInformation("Apify: Run {RunId} has been created for task {TaskId}.", runId, taskId);
            }
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
        return runId;
    }

    public async Task<bool> UpdateTaskInputUrlsAsync(string taskId, List<string> urls, CancellationToken cancellationToken = default)
    {
        try
        {
            var taskInputResponse = await tasksApi.ActorTaskInputGetAsync(taskId, cancellationToken);

            var taskInput = taskInputResponse.ToWebScraperInput();
            var startUrls = urls.Select(u => new StartUrl(u, "GET")).ToList();
            if (taskInput.startUrls == null)
            {
                taskInput.startUrls = startUrls;
            }
            else
            {
                taskInput.startUrls.AddRange(startUrls);
            }

            await tasksApi.ActorTaskInputPutAsync(taskId, taskInput, cancellationToken);
            return true;
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            return false;
        }
    }

    public async Task<string> DeleteTaskAsync(string taskId, CancellationToken cancellationToken = default)
    {
        try
        {
            await tasksApi.ActorTaskDeleteAsync(taskId, cancellationToken);
            // TBD: Response is always NULL. Investigate why.
            logger.LogInformation("Apify: Task {TaskId} has been deleted.", taskId);
            return "DELETED";
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task<GetStoreResponse> GetKeyValueStoreAsync(string storeId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await keyValueStoresApi.KeyValueStoreGetAsync(storeId, cancellationToken);
            return response;
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task<DatasetResponse> GetDatasetAsync(string datasetId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await datasetsApi.DatasetGetAsync(datasetId, cancellationToken: cancellationToken);
            return response;
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task DeleteActorRunAsync(string runId, CancellationToken cancellationToken = default)
    {
        Guard.IsNotNullOrEmpty(runId);

        var runResponse = await GetRunAsync(runId, cancellationToken);
        if (runResponse == null)
        {
            logger.LogInformation("Apify: Run {RunId} does not exists.", runId);
            return;
        }

        try
        {
            await runsApi.ActorRunDeleteAsync(runId, cancellationToken);
            logger.LogInformation("Apify: Run {RunId} has been deleted.", runId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Apify: An error occured: {ErrorMessage}", ex.Message);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task TransferFilesAsync(string dataSetId, string keyValueStoreId, IDownloadStorage storage, CancellationToken cancellationToken = default)
    {
        Guard.IsNotNullOrEmpty(dataSetId);
        Guard.IsNotNullOrEmpty(keyValueStoreId);

        try
        {
            var response = await datasetsApi.DatasetItemsGetAsync(dataSetId, "json", cancellationToken: cancellationToken);
            if (response != null)
            {
                await downloadService.DownloadItemsAsync(dataSetId, response, storage, cancellationToken);
                logger.LogInformation("Apify: DataSetItems for dataset {DataSetId} have been downloaded.", dataSetId);
            }

            var storeResponse = await GetKeyValueStoreKeysAsync(keyValueStoreId, cancellationToken);
            if (storeResponse != null)
            {
                //to be refactored
                string requestTemplateUrl = $"https://api.apify.com/v2/key-value-stores/{keyValueStoreId}/records/##key##?token={apifyConfiguration.AccessToken}";

                await downloadService.BinaryDownloadAsync(httpClient, requestTemplateUrl, storeResponse.Data.Items.Select(x => x.Key).ToArray(), storage, cancellationToken);

                logger.LogInformation("Apify: Binary files for KeyValueStore {KeyValueStoreId} have been downloaded.", keyValueStoreId);
            }
        }
        catch (Exception ex) when (ex is not ArgumentException)
        {
            logger.LogError(ex, "An error occurred: {ErrorMessage}", ex.Message);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task<GetListOfKeyValueStoresResponse> GetKeyValueStoresAsync(CancellationToken cancellationToken = default)
    {
        double? offset = null;
        double? limit = 1;
        bool? desc = null;
        bool? unnamed = true;

        try
        {
            var response = await keyValueStoresApi.KeyValueStoresGetAsync(offset, limit, desc, unnamed, cancellationToken);
            return response;
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task<GetListOfKeysResponse> GetKeyValueStoreKeysAsync(string storeId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await keyValueStoresApi.KeyValueStoreKeysGetAsync(storeId, cancellationToken: cancellationToken);
            return response;
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task<ScheduleResponse> CreateSchedulesAsync(string taskId, string scheduleName, string cronExpression, string timeZone, CancellationToken cancellationToken = default)
    {
        var scheduleCreate = new ScheduleCreate
        {
            Name = scheduleName,
            CronExpression = cronExpression,
            Timezone = timeZone,
            IsEnabled = true,
            IsExclusive = true
        };
        var actions = new ScheduleCreateActions("RUN_ACTOR_TASK", taskId);
        scheduleCreate.Actions = [actions];

        try
        {
            var response = await schedulesApi.SchedulesPostAsync(scheduleCreate, cancellationToken);

            if (response != null)
            {
                logger.LogInformation("Apify: Schedule {ScheduleId} has been created for task {TaskId}.",
                    response.Data.Id, taskId);
            }
            else
            {
                logger.LogWarning("Apify; Schedule for task {TaskId} could not be created.", taskId);
            }
            return response!;
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task<GetListOfSchedulesResponse> GetSchedulesAsync(CancellationToken cancellationToken = default)
    {
        double? offset = null;
        double? limit = null;
        bool? desc = null;

        try
        {
            var response = await schedulesApi.SchedulesGetAsync(offset, limit, desc, cancellationToken);
            return response;
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task<string> DeleteScheduleAsync(string scheduleId, CancellationToken cancellationToken = default)
    {
        try
        {
            await schedulesApi.ScheduleDeleteAsync(scheduleId, cancellationToken);
            // TBD: Response is always NULL. Investigate why.
            logger.LogInformation("Apify: Schedule {ScheduleId} has been deleted.", scheduleId);
            return "DELETED";
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task<RunResponse> GetRunAsync(string runId, CancellationToken cancellationToken = default)
    {
        double? waitForFinish = null;

        RunResponse? response = null;
        if (!string.IsNullOrEmpty(runId)) 
        {
            try
            {
                response = await runsApi.ActorRunGetAsync(runId, waitForFinish, cancellationToken);
            }
            catch (ApiException ex)
            {
                logger.LogApiException(ex);
            }
        }
        
        return response!;
    }

    public async Task<CreateWebhookResponse> CreateWebhookAsync(string url, string taskId, CancellationToken cancellationToken = default)
    {
        if(string.IsNullOrEmpty(apifyConfiguration.WebhookSecurityToken))
        {
            logger.LogError("Apify: WebhookSecurityToken can't be null oe empty");
            throw new ArgumentException("WebhookSecurityToken can't be null oe empty", "WebhookSecurityToken");
        }
        
        var condition = new WebhookCondition
        {
            ActorTaskId = taskId
        };

        List<string> eventTypes =
        [
            "ACTOR.RUN.SUCCEEDED",
            "ACTOR.RUN.ABORTED",
            "ACTOR.RUN.TIMED_OUT"
        ];


        var webhookCreate = new WebhookCreate
        {
            IsAdHoc = false,
            RequestUrl = url,
            Condition = condition,
            EventTypes = eventTypes,
            IgnoreSslErrors = false,
            DoNotRetry = false,
            ShouldInterpolateStrings = true
        };

#if DEBUG
        webhookCreate.IgnoreSslErrors=true;
#endif

        const string payloadTemplate =
            "{ \"userId\": {{userId}}, \"createdAt\": {{createdAt}}, \"eventType\": {{eventType}}, \"eventData\": {{eventData}}, \"resource\": {{resource}} }";
        webhookCreate.PayloadTemplate = payloadTemplate;

        string headersTemplate = "{\"Authorization\": \"Bearer " + apifyConfiguration.WebhookSecurityToken + "\"}";
        webhookCreate.HeadersTemplate = headersTemplate;

        CreateWebhookResponse? response = null;
        
        try
        {
            response = await webhooksApi.WebhooksPostAsync(webhookCreate, cancellationToken: cancellationToken);
            if (response != null) logger.LogInformation("Apify: Webhook {WebhookId} has been created for task {TaskId}.", response.Data.Id, taskId);
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
        return response!;
    }

    public async Task<string> DeleteWebhookAsync(string webhookId, CancellationToken cancellationToken = default)
    {
        try
        {
            await webhooksApi.WebhookDeleteAsync(webhookId, cancellationToken);
            // TDB: response is always NULL. Investigate
            logger.LogInformation("Apify: Webhook {WebhookId} has been deleted.", webhookId);
            return "DELETED";
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task<GetListOfWebhooksResponse> GetWebhooksAsync(CancellationToken cancellationToken = default)
    {
        double? offset = null;
        double? limit = null;
        bool? desc = null;

        try
        {
            var response = await webhooksApi.WebhooksGetAsync(offset, limit, desc, cancellationToken);
            return response;
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task<string> GetLogAsync(string runId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await logsApi.LogGetAsync(runId, stream: false, download: true, cancellationToken);
            return response;
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public async Task RunScrapeAsync(
      string taskName,
      ICrawlerData crawlerData,
      string webhookUrl,
      Func<BatchInfo, Task> callbackFunction,
      CancellationToken cancellationToken = default)
    {
        Guard.IsNotNullOrEmpty(taskName);
        Guard.IsNotNullOrEmpty(webhookUrl);

        var urlItems = await crawlerData.GetStartUrlsAsync();

        if (!urlItems.Any())
        {
            logger.LogInformation("Apify: RunScrapeAsync urlItems is empty.");
            return;
        }

        // Grouping by Domain, MaxCrawlDepth and Configuration  
        var groupedItems = urlItems.GroupBy(static item => new { item.Domain, item.ConfigurationName, item.MaxCrawlDepth });

        logger.LogInformation("Apify: RunScrapeAsync {TaskName} started. Grouped items count {GroupedItems}.", taskName, groupedItems.Count());

        var globalBatchNo = 0;
        foreach (var group in groupedItems)
        {
            var items = group.ToList();
            logger.LogInformation("Apify: RunScrapeAsync {TaskName} processing {Group} group.", taskName, group.Key);

            // Calculate total batches for this group
            var totalBatchesInGroup = (int)Math.Ceiling((double)items.Count / MaxBatchSize);

            // Process the items in batches of maxBatchSize
            for (var i = 0; i < items.Count; i += MaxBatchSize)
            {
                try
                {
                    var batch = items.Skip(i).Take(MaxBatchSize).ToList();

                    // Load config options, need to ensure caching etc etc
                    var input = await crawlerConfigurationService.GetCrawlerConfigurationAsync(crawlerData,
                        group.Key.ConfigurationName);

                    // Override loaded config with options
                    input.StartUrls = [.. batch.Select(c => new StartURLsInner(c.Url))];
                    input.MaxCrawlDepth = group.Key.MaxCrawlDepth;

                    var taskId = await CreateTaskAsync(taskName + "-" + globalBatchNo, input, cancellationToken);
                    await CreateWebhookAsync(webhookUrl, taskId, cancellationToken);
                    var runId = await RunTaskAsync(taskId, cancellationToken);

                    var batchInfo = new BatchInfo
                    {
                        BatchNumber = globalBatchNo,
                        RunId = runId,
                        URLs = batch.Select(item => item.Url).ToList(),
                        TotalBatchesInGroup = totalBatchesInGroup
                    };

                    globalBatchNo++;

                    await callbackFunction(batchInfo);
                    logger.LogInformation("Apify: RunScrapeAsync {TaskName}. Task {TaskId} created, RunId {RunId} created.", taskName, taskId, runId);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "An error occurred: Group Name: {Group}, Error: {ErrorMessage}", group.Key, ex.Message);
                }
            }
        }

        logger.LogInformation("Apify: RunScrapeAsync {TaskName} ended.", taskName);
    }

    public async Task<ActorRunResult> GetActorRunResultsAsync(string runId, CancellationToken cancellationToken = default)
    {
        try
        {
            Guard.IsNotNullOrEmpty(runId.Trim());

            var runResponse = await runsApi.ActorRunGetAsync(runId, null, cancellationToken);
            if (runResponse is null)
            {
                logger.LogError("Actor run with ID '{RunId}' not found", runId);
                throw new InvalidOperationException($"Actor run with ID '{runId}' not found");
            }

            logger.LogInformation("Retrieving actor run results for run ID: {RunId}", runId);

            var actorRunResult = new ActorRunResult
            {
                RunId = runResponse.Data.Id,
                Status = runResponse.Data.Status,
                StatusMessage = runResponse.Data.StatusMessage,
                StartedAt = runResponse.Data.StartedAt,
                FinishedAt = runResponse.Data.FinishedAt
            };

            return actorRunResult;
        }
        catch (Exception ex) when (ex is not ArgumentException)
        {
            logger.LogError(ex, "Failed to retrieve actor run details for run ID: {RunId}", runId);
            throw new InvalidOperationException($"Failed to retrieve actor run details for run ID '{runId}': {ex.Message}", ex);
        }
    }

    public async Task<List<DatasetItem?>> GetDatasetItemsAsync(string datasetId, CancellationToken cancellationToken = default)
    {
        try
        {
            Guard.IsNotNullOrEmpty(datasetId.Trim());

            var datasetItemsResponse = await datasetService.GetDatasetItemsAsync(datasetId, cancellationToken);

            if (datasetItemsResponse is null)
            {
                logger.LogError("Dataset items with ID '{DatasetId}' not found", datasetId);
                throw new InvalidOperationException($"Dataset items with ID '{datasetId}' not found");
            }

            var datasetItems = datasetService.ProcessDatasetItemsResponse(datasetItemsResponse);

            return datasetItems;
        }
        catch (Exception ex) when (ex is not ArgumentException)
        {
            logger.LogError(ex, "Failed to retrieve actor run results for dataset ID: {RunId}", datasetId);
            throw new InvalidOperationException($"Failed to retrieve actor run results for dataset ID '{datasetId}': {ex.Message}", ex);
        }
    }

    public async Task CleanupActorRunsAsync(string actorId, TimeSpan age, CancellationToken cancellationToken = default)
    {
        Guard.IsNotNullOrEmpty(actorId);

        // TODO: The endpoint will not return more than 1000 elements, need to implement pagination to get full set of runs
        var dateDeleteBefore = DateTime.UtcNow.Subtract(age);

        var offset = 0;
        var limit = 1000;
        var response = await runsApi.ActorRunsGetAsync(offset, limit, null, null, cancellationToken);

        if (response != null && response.Data.Items.Count > 0)
        {
            foreach (var item in response.Data.Items.Where(x => x.ActId == actorId))
            {
                await DeleteRunResourcesAsync(item, dateDeleteBefore, cancellationToken);
            }
        }
    }

    private async Task<string> CreateTaskAsync(string taskName, InputSchema input, CancellationToken cancellationToken = default)
    {
        var tasksOptions = new CreateTaskRequestOptions
        {
            Build = null!,
            TimeoutSecs = defaultTimeoutSeconds,
            MemoryMbytes = defaultMemoryBytes
        };

        var taskId = string.Empty;
        try
        {
            var actorTasksPostRequest = new ActorTasksPostRequest(Actors.WebsiteContentCrawler, taskName, tasksOptions, null!);
            var response = await tasksApi.ActorTasksPostAsync(actorTasksPostRequest,cancellationToken);
            if (response != null)
            {
                await tasksApi.ActorTaskInputPutAsync(response.Data.Id, input, cancellationToken);
                taskId = response.Data.Id;
                logger.LogInformation("Apify: Task {TaskId} has been created.", taskId);
            }
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
        return taskId;
    }

    private async Task DeleteRunResourcesAsync(RunShort item, DateTime dateDeleteBefore, CancellationToken cancellationToken = default)
    {        
        try
        {
            if (DateTime.TryParse(item.FinishedAt, CultureInfo.InvariantCulture, DateTimeStyles.RoundtripKind, out var runDate) && runDate <= dateDeleteBefore)
            {
                if (!string.IsNullOrEmpty(item.ActorTaskId)) await DeleteTaskAsync(item.ActorTaskId, cancellationToken);
                await runsApi.ActorRunDeleteAsync(item.Id, cancellationToken);
                logger.LogInformation("Apify: Resources for {RunId} have been deleted", item.Id);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred cleaning up resources for run: {RunId} - {ErrorMessage}", item.Id, ex.Message);
        }
    }
}
