using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook;
using Phlex.Core.Apify.Webhook.Controllers;
using Phlex.Core.Apify.Webhook.Models;
using Shouldly;
using Microsoft.AspNetCore.Mvc;

namespace Phlex.Core.Apify.IntegrationTests.Webhooks;


[Collection(TestCollectionDefinitions.Apify)]
public class WebhookTests
{
    private readonly IApifyNotification client;
    private readonly IOptions<ApifyWebhookSettings> webhookSettings;
    private readonly ILogger<ApifyWebhookController> logger;


    public WebhookTests()
    {
        this.client = Substitute.For<IApifyNotification>();
        this.logger = Substitute.For<ILogger<ApifyWebhookController>>();
        this.webhookSettings = Options.Create(new ApifyWebhookSettings());
    }

    [Theory]
    [InlineData("SUCCEEDED")]
    [InlineData("FAILED")]
    public async Task Receive_ValidBearerToken_CallsIApifyNotificationClientMethods(string resourceStatus)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventData = new EventData(),
            resource = new Resource()
            {
                status = resourceStatus
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        var actionResult = controller.Receive(payload);
        actionResult.ShouldBeOfType<OkResult>();

        // Assert
        if (resourceStatus == "SUCCEEDED")
            await client.Received(1).RunSucceeded(payload);
        else
            await client.Received(1).RunFailed(payload);
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("ivalid-bearer-token")]
    public async Task Receive_InvalidBearerToken_LogWarning(string? bearerToken)
    {
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventData = new EventData(),
            resource = new Resource()
            {
                status = "SUCCEEDED"
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + bearerToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        var actionResult = controller.Receive(payload);
        actionResult.ShouldBeOfType<UnauthorizedResult>();

        await client.DidNotReceive().RunSucceeded(payload);
        await client.DidNotReceive().RunFailed(payload);
    }
}
