using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook;
using Phlex.Core.Apify.Webhook.Controllers;
using Phlex.Core.Apify.Webhook.Models;
using Shouldly;

namespace Phlex.Core.Apify.IntegrationTests.Webhooks;

[Collection(TestCollectionDefinitions.Apify)]
public class WebhookTests
{
    private readonly IApifyNotification client;
    private readonly IOptions<ApifyWebhookSettings> webhookSettings;
    private readonly ILogger<ApifyWebhookController> logger;


    public WebhookTests()
    {
        this.client = Substitute.For<IApifyNotification>();
        this.logger = Substitute.For<ILogger<ApifyWebhookController>>();
        this.webhookSettings = Options.Create(new ApifyWebhookSettings());
    }

    [Theory]
    [InlineData("SUCCEEDED")]
    [InlineData("FAILED")]
    public async Task Receive_ValidBearerToken_CallsIApifyNotificationClientMethods(string resourceStatus)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventType = resourceStatus == "SUCCEEDED" ? "ACTOR.RUN.SUCCEEDED" : "ACTOR.RUN.ABORTED",
            eventData = new EventData()
            {
                actorRunId = "test-run-id"
            },
            resource = new Resource()
            {
                status = resourceStatus
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        var actionResult = controller.Receive(payload);
        actionResult.ShouldBeOfType<OkResult>();

        // Assert
        if (resourceStatus == "SUCCEEDED")
            await client.Received(1).RunSucceeded(payload);
        else
            await client.Received(1).RunFailed(payload);
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("invalid-bearer-token")]
    public async Task Receive_InvalidBearerToken_LogWarning(string? bearerToken)
    {
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventType = "ACTOR.RUN.SUCCEEDED",
            eventData = new EventData(),
            resource = new Resource()
            {
                status = "SUCCEEDED"
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + bearerToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        var actionResult = controller.Receive(payload);
        actionResult.ShouldBeOfType<UnauthorizedResult>();

        await client.DidNotReceive().RunSucceeded(payload);
        await client.DidNotReceive().RunFailed(payload);
    }

    [Theory]
    [InlineData("INVALID.EVENT.TYPE")]
    [InlineData("ACTOR.RUN.UNKNOWN")]
    [InlineData("ACTOR.RUN.FAILED")]
    [InlineData("")]
    [InlineData(null)]
    public void Receive_InvalidEventType_ReturnsBadRequestAndLogsError(string? invalidEventType)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventType = invalidEventType,
            eventData = new EventData(),
            resource = new Resource()
            {
                status = "SUCCEEDED"
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        var result = controller.Receive(payload);

        // Assert
        Assert.IsType<BadRequestObjectResult>(result);
        var badRequestResult = result as BadRequestObjectResult;
        Assert.Contains("Invalid event type", badRequestResult?.Value?.ToString());

        logger.Received(1).Log(
            LogLevel.Error,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Invalid event type")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Fact]
    public void Receive_NullPayload_ReturnsBadRequestAndLogsError()
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        ApifyWebhookPayload? payload = null;

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var httpContext = new DefaultHttpContext(features);
        controller.ControllerContext = new ControllerContext()
        {
            HttpContext = httpContext
        };

        // Act
        var result = controller.Receive(payload);

        // Assert
        Assert.IsType<BadRequestObjectResult>(result);
        var badRequestResult = result as BadRequestObjectResult;
        Assert.Equal("Invalid payload: payload is null", badRequestResult?.Value);

        logger.Received(1).Log(
            LogLevel.Error,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Received null payload")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Theory]
    [InlineData("missing_eventData")]
    [InlineData("missing_resource")]
    [InlineData("missing_identifiers")]
    public void Receive_InvalidPayloadStructure_ReturnsBadRequestAndLogsError(string invalidType)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);

        var payload = new ApifyWebhookPayload()
        {
            eventType = Phlex.Core.Apify.Constants.ApifyEventTypes.ActorRunSucceeded
        };

        switch (invalidType)
        {
            case "missing_eventData":
                payload.eventData = null;
                payload.resource = new Resource() { status = "SUCCEEDED" };
                break;
            case "missing_resource":
                payload.eventData = new EventData() { actorRunId = "test-run-id" };
                payload.resource = null;
                break;
            case "missing_identifiers":
                payload.eventData = new EventData();
                payload.resource = new Resource() { status = "SUCCEEDED" };
                break;
        }

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        var result = controller.Receive(payload);

        // Assert
        Assert.IsType<BadRequestObjectResult>(result);
        var badRequestResult = result as BadRequestObjectResult;
        Assert.Contains("Invalid payload: missing required fields", badRequestResult?.Value?.ToString());

        logger.Received(1).Log(
            LogLevel.Error,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Invalid payload structure")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Theory]
    [InlineData(Apify.Constants.ApifyEventTypes.ActorRunSucceeded)]
    [InlineData(Apify.Constants.ApifyEventTypes.ActorRunAborted)]
    [InlineData(Apify.Constants.ApifyEventTypes.ActorRunTimedOut)]
    public void Receive_ValidEventType_ProcessesSuccessfully(string validEventType)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventType = validEventType,
            eventData = new EventData()
            {
                actorRunId = "test-run-id"
            },
            resource = new Resource()
            {
                status = validEventType == Apify.Constants.ApifyEventTypes.ActorRunSucceeded ? "SUCCEEDED" : "ABORTED"
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        var result = controller.Receive(payload);

        // Assert
        Assert.IsType<OkResult>(result);

        if (validEventType == Apify.Constants.ApifyEventTypes.ActorRunSucceeded)
        {
            client.Received(1).RunSucceeded(payload);
        }
        else
        {
            client.Received(1).RunFailed(payload);
        }
    }

    [Theory]
    [InlineData(Apify.Constants.ApifyEventTypes.ActorRunSucceeded, true)]
    [InlineData(Apify.Constants.ApifyEventTypes.ActorRunAborted, false)]
    [InlineData(Apify.Constants.ApifyEventTypes.ActorRunTimedOut, false)]
    public void Receive_EventTypeHandling_CallsCorrectClientMethod(string eventType, bool shouldCallRunSucceeded)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventType = eventType,
            eventData = new EventData()
            {
                actorRunId = "test-run-id"
            },
            resource = new Resource()
            {
                status = "SUCCEEDED"
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        var result = controller.Receive(payload);

        // Assert
        Assert.IsType<OkResult>(result);

        if (shouldCallRunSucceeded)
        {
            client.Received(1).RunSucceeded(payload);
            client.DidNotReceive().RunFailed(Arg.Any<ApifyWebhookPayload>());
        }
        else
        {
            client.Received(1).RunFailed(payload);
            client.DidNotReceive().RunSucceeded(Arg.Any<ApifyWebhookPayload>());
        }
    }
}
