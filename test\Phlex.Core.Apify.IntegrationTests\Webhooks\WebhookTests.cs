using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook;
using Phlex.Core.Apify.Webhook.Controllers;
using Phlex.Core.Apify.Webhook.Models;

namespace Phlex.Core.Apify.IntegrationTests.Webhooks;


[Collection(TestCollectionDefinitions.Apify)]
public class WebhookTests
{
    private readonly IApifyNotification client;
    private readonly IOptions<ApifyWebhookSettings> webhookSettings;
    private readonly ILogger<ApifyWebhookController> logger;


    public WebhookTests()
    {
        this.client = Substitute.For<IApifyNotification>();
        this.logger = Substitute.For<ILogger<ApifyWebhookController>>();
        this.webhookSettings = Options.Create(new ApifyWebhookSettings());
    }

    [Theory]
    [InlineData("SUCCEEDED")]
    [InlineData("FAILED")]
    public async Task Receive_ValidBearerToken_CallsIApifyNotificationClientMethods(string resourceStatus)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventType = resourceStatus == "SUCCEEDED" ? "ACTOR.RUN.SUCCEEDED" : "ACTOR.RUN.ABORTED",
            eventData = new EventData()
            {
                actorRunId = "test-run-id"
            },
            resource = new Resource()
            {
                status = resourceStatus
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        controller.Receive(payload);

        // Assert
        if (resourceStatus == "SUCCEEDED")
            await client.Received(1).RunSucceeded(payload);
        else
            await client.Received(1).RunFailed(payload);
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("ivalid-bearer-token")]
    public async Task Receive_InvalidBearerToken_LogWarning(string? bearerToken)
    {
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventType = "ACTOR.RUN.SUCCEEDED",
            eventData = new EventData(),
            resource = new Resource()
            {
                status = "SUCCEEDED"
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + bearerToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        await client.DidNotReceive().RunSucceeded(payload);
        await client.DidNotReceive().RunFailed(payload);
    }

    [Theory]
    [InlineData("INVALID.EVENT.TYPE")]
    [InlineData("ACTOR.RUN.UNKNOWN")]
    [InlineData("ACTOR.RUN.FAILED")]
    [InlineData("")]
    [InlineData(null)]
    public void Receive_InvalidEventType_ReturnsBadRequestAndLogsError(string? invalidEventType)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventType = invalidEventType,
            eventData = new EventData(),
            resource = new Resource()
            {
                status = "SUCCEEDED"
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        var result = controller.Receive(payload);

        // Assert
        Assert.IsType<BadRequestObjectResult>(result);
        var badRequestResult = result as BadRequestObjectResult;
        Assert.Contains("Invalid event type", badRequestResult?.Value?.ToString());

        // Verify error was logged
        logger.Received(1).Log(
            LogLevel.Error,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Invalid event type")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Fact]
    public void Receive_NullPayload_ReturnsBadRequestAndLogsError()
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        ApifyWebhookPayload? payload = null;

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var httpContext = new DefaultHttpContext(features);
        controller.ControllerContext = new ControllerContext()
        {
            HttpContext = httpContext
        };

        // Act
        var result = controller.Receive(payload);

        // Assert
        Assert.IsType<BadRequestObjectResult>(result);
        var badRequestResult = result as BadRequestObjectResult;
        Assert.Equal("Invalid payload: payload is null", badRequestResult?.Value);

        // Verify error was logged
        logger.Received(1).Log(
            LogLevel.Error,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Received null payload")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Theory]
    [InlineData("missing_eventData")]
    [InlineData("missing_resource")]
    [InlineData("missing_identifiers")]
    public void Receive_InvalidPayloadStructure_ReturnsBadRequestAndLogsError(string invalidType)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);

        var payload = new ApifyWebhookPayload()
        {
            eventType = "ACTOR.RUN.SUCCEEDED"
        };

        // Create different types of invalid payloads
        switch (invalidType)
        {
            case "missing_eventData":
                payload.eventData = null;
                payload.resource = new Resource();
                break;
            case "missing_resource":
                payload.eventData = new EventData { actorRunId = "test-run-id" };
                payload.resource = null;
                break;
            case "missing_identifiers":
                payload.eventData = new EventData(); // No actorId, actorTaskId, or actorRunId
                payload.resource = new Resource();
                break;
        }

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var httpContext = new DefaultHttpContext(features);
        controller.ControllerContext = new ControllerContext()
        {
            HttpContext = httpContext
        };

        // Act
        var result = controller.Receive(payload);

        // Assert
        Assert.IsType<BadRequestObjectResult>(result);
        var badRequestResult = result as BadRequestObjectResult;
        Assert.Equal("Invalid payload: missing required fields", badRequestResult?.Value);

        // Verify error was logged
        logger.Received(1).Log(
            LogLevel.Error,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Invalid payload structure")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Theory]
    [InlineData("ACTOR.RUN.SUCCEEDED")]
    [InlineData("ACTOR.RUN.ABORTED")]
    [InlineData("ACTOR.RUN.TIMED_OUT")]
    public void Receive_ValidEventType_ProcessesSuccessfully(string validEventType)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventType = validEventType,
            eventData = new EventData()
            {
                actorRunId = "test-run-id"
            },
            resource = new Resource()
            {
                status = validEventType == "ACTOR.RUN.SUCCEEDED" ? "SUCCEEDED" : "ABORTED"
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        var result = controller.Receive(payload);

        // Assert
        Assert.IsType<OkResult>(result);

        // Verify appropriate client method was called based on event type
        if (validEventType == "ACTOR.RUN.SUCCEEDED")
            client.Received(1).RunSucceeded(payload);
        else // ACTOR.RUN.ABORTED and ACTOR.RUN.TIMED_OUT should call RunFailed
            client.Received(1).RunFailed(payload);
    }

    [Theory]
    [InlineData("ACTOR.RUN.SUCCEEDED", true)]
    [InlineData("ACTOR.RUN.ABORTED", false)]
    [InlineData("ACTOR.RUN.TIMED_OUT", false)]
    public void Receive_EventTypeHandling_CallsCorrectClientMethod(string eventType, bool shouldCallRunSucceeded)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventType = eventType,
            eventData = new EventData()
            {
                actorRunId = "test-run-id"
            },
            resource = new Resource()
            {
                status = "SUCCEEDED" // Resource status shouldn't matter, only eventType
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        var result = controller.Receive(payload);

        // Assert
        Assert.IsType<OkResult>(result);

        // Verify correct client method was called based on event type
        if (shouldCallRunSucceeded)
        {
            client.Received(1).RunSucceeded(payload);
            client.DidNotReceive().RunFailed(Arg.Any<ApifyWebhookPayload>());
        }
        else
        {
            client.Received(1).RunFailed(payload);
            client.DidNotReceive().RunSucceeded(Arg.Any<ApifyWebhookPayload>());
        }
    }
}
