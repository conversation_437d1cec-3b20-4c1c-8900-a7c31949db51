using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook;
using Phlex.Core.Apify.Webhook.Controllers;
using Phlex.Core.Apify.Webhook.Models;

namespace Phlex.Core.Apify.IntegrationTests.Webhooks;


[Collection(TestCollectionDefinitions.Apify)]
public class WebhookTests
{
    private readonly IApifyNotification client;
    private readonly IOptions<ApifyWebhookSettings> webhookSettings;
    private readonly ILogger<ApifyWebhookController> logger;


    public WebhookTests()
    {
        this.client = Substitute.For<IApifyNotification>();
        this.logger = Substitute.For<ILogger<ApifyWebhookController>>();
        this.webhookSettings = Options.Create(new ApifyWebhookSettings());
    }

    [Theory]
    [InlineData("SUCCEEDED")]
    [InlineData("FAILED")]
    public async Task Receive_ValidBearerToken_CallsIApifyNotificationClientMethods(string resourceStatus)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventType = resourceStatus == "SUCCEEDED" ? "ACTOR.RUN.SUCCEEDED" : "ACTOR.RUN.FAILED",
            eventData = new EventData(),
            resource = new Resource()
            {
                status = resourceStatus
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        controller.Receive(payload);

        // Assert
        if (resourceStatus == "SUCCEEDED")
            await client.Received(1).RunSucceeded(payload);
        else
            await client.Received(1).RunFailed(payload);
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("ivalid-bearer-token")]
    public async Task Receive_InvalidBearerToken_LogWarning(string? bearerToken)
    {
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventData = new EventData(),
            resource = new Resource()
            {
                status = "SUCCEEDED"
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + bearerToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        await client.DidNotReceive().RunSucceeded(payload);
        await client.DidNotReceive().RunFailed(payload);
    }

    [Theory]
    [InlineData("INVALID.EVENT.TYPE")]
    [InlineData("ACTOR.RUN.UNKNOWN")]
    [InlineData("")]
    [InlineData(null)]
    public void Receive_InvalidEventType_ReturnsBadRequestAndLogsError(string? invalidEventType)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventType = invalidEventType,
            eventData = new EventData(),
            resource = new Resource()
            {
                status = "SUCCEEDED"
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        var result = controller.Receive(payload);

        // Assert
        Assert.IsType<BadRequestObjectResult>(result);
        var badRequestResult = result as BadRequestObjectResult;
        Assert.Contains("Invalid event type", badRequestResult?.Value?.ToString());

        // Verify error was logged
        logger.Received(1).Log(
            LogLevel.Error,
            Arg.Any<EventId>(),
            Arg.Is<object>(o => o.ToString()!.Contains("Invalid event type")),
            Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }

    [Theory]
    [InlineData("ACTOR.RUN.SUCCEEDED")]
    [InlineData("ACTOR.RUN.FAILED")]
    [InlineData("ACTOR.RUN.ABORTED")]
    [InlineData("ACTOR.RUN.TIMED_OUT")]
    public async Task Receive_ValidEventType_ProcessesSuccessfully(string validEventType)
    {
        // Arrange
        webhookSettings.Value.SecretToken = "secret-token";
        var controller = new ApifyWebhookController(client, webhookSettings, logger);
        var payload = new ApifyWebhookPayload()
        {
            eventType = validEventType,
            eventData = new EventData(),
            resource = new Resource()
            {
                status = validEventType == "ACTOR.RUN.SUCCEEDED" ? "SUCCEEDED" : "FAILED"
            }
        };

        var req = new HttpRequestFeature();
        req.Headers.Authorization = "Bearer " + webhookSettings.Value.SecretToken;
        var features = new FeatureCollection();
        features.Set<IHttpRequestFeature>(req);
        var context = new DefaultHttpContext(features);
        controller.ControllerContext.HttpContext = context;

        // Act
        var result = controller.Receive(payload);

        // Assert
        Assert.IsType<OkResult>(result);

        // Verify appropriate client method was called
        if (validEventType == "ACTOR.RUN.SUCCEEDED")
            await client.Received(1).RunSucceeded(payload);
        else
            await client.Received(1).RunFailed(payload);
    }
}
