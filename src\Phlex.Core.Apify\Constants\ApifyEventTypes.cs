namespace Phlex.Core.Apify.Constants;

public static class ApifyEventTypes
{

    public const string ActorRunSucceeded = "ACTOR.RUN.SUCCEEDED";
    public const string ActorRunAborted = "ACTOR.RUN.ABORTED";
    public const string ActorRunTimedOut = "ACTOR.RUN.TIMED_OUT";

    public static readonly string[] ValidEventTypes = 
    [
        ActorRunSucceeded,
        ActorRunAborted,
        ActorRunTimedOut
    ];

    public static List<string> GetValidEventTypesList()
    {
        return [.. ValidEventTypes];
    }

    public static bool IsValidEventType(string? eventType)
    {
        if (string.IsNullOrEmpty(eventType))
            return false;

        return ValidEventTypes.Contains(eventType);
    }
}
