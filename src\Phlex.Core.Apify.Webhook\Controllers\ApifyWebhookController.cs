using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook.Models;

namespace Phlex.Core.Apify.Webhook.Controllers
{
    [ApiController]
    [AllowAnonymous]
    [IgnoreAntiforgeryToken]
    [Route("api/webhook/apify")]
    public class ApifyWebhookController : ControllerBase
    {
        private readonly IApifyNotification client;
        private readonly ApifyWebhookSettings webhookSettings;

        private readonly ILogger<ApifyWebhookController> logger;

        public ApifyWebhookController(IApifyNotification client,
            IOptions<ApifyWebhookSettings> apifyWebhookSettings,
            ILogger<ApifyWebhookController> logger)
        {
            this.client = client;
            this.webhookSettings = apifyWebhookSettings.Value;
            this.logger = logger;
        }

        [HttpPost]
        public IActionResult Receive([FromBody] ApifyWebhookPayload payload)
        {
            // Validate payload is not null
            if (payload == null)
            {
                logger.LogError("Apify: Received null payload");
                return BadRequest("Invalid payload: payload is null");
            }

            string? token = Request.Headers.Authorization;

            if (token != null && token.Equals("Bearer " + webhookSettings.SecretToken))
            {
                // Validate event type
                if (!IsValidEventType(payload.eventType))
                {
                    logger.LogError("Apify: Invalid event type received: {EventType}", payload.eventType ?? "null");
                    return BadRequest($"Invalid event type: {payload.eventType ?? "null"}");
                }

                // Validate required fields
                if (!IsValidPayload(payload))
                {
                    logger.LogError("Apify: Invalid payload structure received");
                    return BadRequest("Invalid payload: missing required fields");
                }

                // Handle different event types
                switch (payload.eventType)
                {
                    case "ACTOR.RUN.SUCCEEDED":
                        client.RunSucceeded(payload);
                        break;
                    case "ACTOR.RUN.ABORTED":
                    case "ACTOR.RUN.TIMED_OUT":
                        client.RunFailed(payload);
                        break;
                    default:
                        // This should not happen due to validation above, but handle gracefully
                        logger.LogWarning("Apify: Unexpected event type after validation: {EventType}", payload.eventType);
                        client.RunFailed(payload);
                        break;
                }
            }
            else
            {
                string? ip = this.HttpContext.GetServerVariable("REMOTE_HOST");
                if (ip == null)
                {
                    ip = this.HttpContext.GetServerVariable("REMOTE_ADDR");
                    if (ip == null) ip = "null";
                }
                logger.LogWarning("Apify: Call to webhook receiver enpoint without valid security token. Remote IP: {IP}", ip);
            }
            return Ok();
        }

        private static bool IsValidEventType(string? eventType)
        {
            if (string.IsNullOrEmpty(eventType))
                return false;

            var validEventTypes = new[]
            {
                "ACTOR.RUN.SUCCEEDED",
                "ACTOR.RUN.ABORTED",
                "ACTOR.RUN.TIMED_OUT"
            };

            return validEventTypes.Contains(eventType);
        }

        private static bool IsValidPayload(ApifyWebhookPayload payload)
        {
            // Check for required fields based on Apify webhook structure
            if (payload.eventData == null)
                return false;

            if (payload.resource == null)
                return false;

            // eventData should have at least one of these identifiers
            if (string.IsNullOrEmpty(payload.eventData.actorId) &&
                string.IsNullOrEmpty(payload.eventData.actorTaskId) &&
                string.IsNullOrEmpty(payload.eventData.actorRunId))
                return false;

            return true;
        }
    }
}
